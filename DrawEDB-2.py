import webview
import os
import sys
import threading
import time
import shutil
from pathlib import Path
from watchdog.observers import Observer
import platform
import subprocess
import uuid
import hashlib
import json
import base64
from datetime import datetime
from tkinter import messagebox, simpledialog
import tkinter as tk
from tkinter import ttk

# 文件监控相关
try:
    from watchdog.events import FileSystemEventHandler
except ImportError:
    # 临时的Tk根，用于显示错误消息框
    _err_root = tk.Tk()
    _err_root.withdraw()
    messagebox.showerror("错误", "请安装watchdog库：pip install watchdog")
    _err_root.destroy()
    exit(1)

# RSA加密相关
try:
    from cryptography.hazmat.primitives.asymmetric import padding
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.backends import default_backend
except ImportError:
    # 临时的Tk根，用于显示错误消息框
    _err_root = tk.Tk()
    _err_root.withdraw()
    messagebox.showerror("错误", "请安装cryptography库：pip install cryptography")
    _err_root.destroy()
    exit(1)

# 嵌入的公钥
EMBEDDED_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAm8ZpHHGuEo+1c/wxh7Gp
fGBRMWNDE40ghHOikovU2fiN53PQutszoTQRiZtTVcs3CRp9KNBbVnk7eukJ8adB
y6q1oZBLLQYma5padzViFa1SZffGD8issZXvlj7IqLz8wRyVZsphCmovUf9p2qap
2CaZtIxcY66hRRVgreoB394yh4oLAh/dboqf6NKWdsErgFyD4rHL8KN7coUJBFcs
xwqfDnhbLYmH+vpXEBPMaiWdS3JqiADhU1XDQMJSBIzTu+PfYvGvj9x/Z1bIvNBF
TeTOwY2RB2m7JNRRMUqlLr2dPRQpbV/jrDLuExoytbpBK1QpNXTu8YgFGhYt+1U2
9QIDAQAB
-----END PUBLIC KEY-----"""

# ================== 路径配置 ==================
# 确定应用程序的基础路径，用于存储许可证文件和下载的SQL文件。
# - 如果程序被打包成可执行文件（frozen），基础路径是可执行文件所在的目录。
# - 如果程序作为普通Python脚本运行，基础路径是脚本文件所在的目录。
if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
    # 程序被打包 (例如通过PyInstaller)
    # sys.executable 是可执行文件的路径
    _APP_BASE_PATH = os.path.dirname(sys.executable)
else:
    # 程序作为普通脚本运行
    # __file__ 是当前脚本的路径
    _APP_BASE_PATH = os.path.dirname(os.path.abspath(__file__))

SCRIPT_DIR = _APP_BASE_PATH  # 应用数据（包括许可证和下载文件）的存储目录
LICENSE_FILE = os.path.join(SCRIPT_DIR, ".license.dat")


# ================== 机器信息获取模块 ==================
class MachineInfo:
    @staticmethod
    def get_cpu_id():
        """获取CPU ID"""
        try:
            if platform.system() == "Windows":
                cmd = "wmic cpu get ProcessorId /format:list"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                for line in result.stdout.split('\n'):
                    if 'ProcessorId=' in line:
                        return line.split('=').strip()
            elif platform.system() == "Linux":
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if 'processor' in line:
                            return line.split(':').strip()
            elif platform.system() == "Darwin":  # macOS
                cmd = "sysctl -n machdep.cpu.brand_string"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                return result.stdout.strip()
        except:
            pass
        return "unknown_cpu"
    
    @staticmethod
    def get_motherboard_serial():
        """获取主板序列号"""
        try:
            if platform.system() == "Windows":
                cmd = "wmic baseboard get SerialNumber /format:list"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line:
                        return line.split('=').strip()
            elif platform.system() == "Linux":
                cmd = "sudo dmidecode -s baseboard-serial-number"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                return result.stdout.strip()
            elif platform.system() == "Darwin":  # macOS
                cmd = "system_profiler SPHardwareDataType | grep 'Serial Number'"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                return result.stdout.split(':').strip()
        except:
            pass
        return "unknown_motherboard"
    
    @staticmethod
    def get_disk_serial():
        """获取硬盘序列号"""
        try:
            if platform.system() == "Windows":
                cmd = "wmic diskdrive get SerialNumber /format:list"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                for line in result.stdout.split('\n'):
                    if 'SerialNumber=' in line and line.split('=').strip():
                        return line.split('=').strip()
            elif platform.system() == "Linux":
                cmd = "lsblk -d -o serial | head -2 | tail -1"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                return result.stdout.strip()
            elif platform.system() == "Darwin":  # macOS
                cmd = "system_profiler SPSerialATADataType | grep 'Serial Number'"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.stdout:
                    return result.stdout.split(':').strip()
        except:
            pass
        return "unknown_disk"
    
    @staticmethod
    def get_mac_address():
        """获取MAC地址"""
        try:
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> ele) & 0xff) 
                           for ele in range(0, 8*6, 8)][::-1])
            return mac
        except:
            return "unknown_mac"
    
    @staticmethod
    def get_machine_code():
        """生成机器码"""
        cpu_id = MachineInfo.get_cpu_id()
        motherboard = MachineInfo.get_motherboard_serial()
        disk_serial = MachineInfo.get_disk_serial()
        mac_addr = MachineInfo.get_mac_address()
        
        # 组合所有硬件信息
        combined = f"{cpu_id}|{motherboard}|{disk_serial}|{mac_addr}|{platform.system()}"
        
        # 生成SHA256哈希并取前16位作为机器码
        hash_obj = hashlib.sha256(combined.encode('utf-8'))
        machine_code = hash_obj.hexdigest()[:16].upper()
        
        # 格式化为XXXX-XXXX-XXXX-XXXX
        formatted_code = '-'.join([machine_code[i:i+4] for i in range(0, 16, 4)])
        
        return formatted_code


# ================== 加密工具模块 ==================
class CryptoUtils:
    @staticmethod
    def load_public_key(pem_data):
        """加载公钥"""
        try:
            return serialization.load_pem_public_key(
                pem_data.encode('utf-8'),
                backend=default_backend()
            )
        except Exception as e:
            raise Exception(f"加载公钥失败：{str(e)}")
    
    @staticmethod
    def verify_signature(public_key, data_bytes, signature_bytes):
        """验证签名"""
        try:
            public_key.verify(
                signature_bytes,
                data_bytes,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except:
            return False
    
    @staticmethod
    def encrypt_data(data, key):
        """简单的数据加密（用于本地存储）"""
        key_hash = hashlib.sha256(key.encode('utf-8')).digest()
        encrypted = bytearray()
        for i, byte in enumerate(data.encode('utf-8')):
            encrypted.append(byte ^ key_hash[i % len(key_hash)])
        return base64.b64encode(encrypted).decode('utf-8')
    
    @staticmethod
    def decrypt_data(encrypted_data, key):
        """简单的数据解密"""
        try:
            key_hash = hashlib.sha256(key.encode('utf-8')).digest()
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted = bytearray()
            for i, byte in enumerate(encrypted_bytes):
                decrypted.append(byte ^ key_hash[i % len(key_hash)])
            return decrypted.decode('utf-8')
        except:
            return None


# ================== 自定义对话框类 ==================
class MachineCodeDialog:
    def __init__(self, machine_code):
        self.machine_code = machine_code
        self.result = False
        
    def show(self):
        """显示机器码对话框"""
        root = tk.Toplevel()
        root.title("机器码信息")
        root.geometry("500x200")
        root.resizable(False, False)
        
        # 居中显示
        root.transient()
        root.grab_set()
        
        # 主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 说明文字
        info_label = ttk.Label(
            main_frame, 
            text="请将下方机器码发送给开发者获取验证码：",
            font=('Arial', 11)
        )
        info_label.pack(pady=(0, 15))
        
        # 机器码输入框（只读，可选择复制）
        code_frame = ttk.Frame(main_frame)
        code_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.code_entry = tk.Entry(
            code_frame,
            font=('Courier New', 12, 'bold'),
            justify='center',
            state='readonly',
            readonlybackground='white',
            fg='blue',
            relief='solid',
            bd=1
        )
        self.code_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.code_entry.config(state='normal')
        self.code_entry.insert(0, self.machine_code)
        self.code_entry.config(state='readonly')
        
        # 复制按钮
        copy_btn = ttk.Button(
            code_frame,
            text="复制",
            command=self.copy_machine_code,
            width=8
        )
        copy_btn.pack(side=tk.RIGHT)
        
        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)
        
        # 确定按钮
        ok_btn = ttk.Button(
            btn_frame,
            text="确定",
            command=lambda: self.close_dialog(root, True),
            width=12
        )
        ok_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # 取消按钮
        cancel_btn = ttk.Button(
            btn_frame,
            text="取消",
            command=lambda: self.close_dialog(root, False),
            width=12
        )
        cancel_btn.pack(side=tk.RIGHT)
        
        # 默认选中机器码文本框内容
        self.code_entry.config(state='normal')
        self.code_entry.select_range(0, tk.END)
        self.code_entry.config(state='readonly')
        self.code_entry.focus()
        
        # 绑定回车键
        root.bind('<Return>', lambda e: self.close_dialog(root, True))
        root.bind('<Escape>', lambda e: self.close_dialog(root, False))
        
        # 等待对话框关闭
        root.wait_window()
        return self.result
    
    def copy_machine_code(self):
        """复制机器码到剪贴板"""
        root = self.code_entry.winfo_toplevel()
        root.clipboard_clear()
        root.clipboard_append(self.machine_code)
        messagebox.showinfo("提示", "机器码已复制到剪贴板！", parent=root)
    
    def close_dialog(self, root, result):
        """关闭对话框"""
        self.result = result
        root.destroy()


class LicenseCodeDialog:
    def __init__(self):
        self.license_code = None
        
    def show(self):
        """显示验证码输入对话框"""
        root = tk.Toplevel()
        root.title("许可证验证")
        root.geometry("600x400")  # 放大2倍
        root.resizable(False, False)
        
        # 居中显示
        root.transient()
        root.grab_set()
        
        # 主框架
        main_frame = ttk.Frame(root, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame,
            text="请输入开发者提供的验证码",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # 验证码输入框
        code_frame = ttk.LabelFrame(main_frame, text="验证码", padding="15")
        code_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        self.code_text = tk.Text(
            code_frame,
            font=('Courier New', 11),
            wrap=tk.WORD,
            height=8,
            relief='solid',
            bd=1
        )
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(code_frame, orient=tk.VERTICAL, command=self.code_text.yview)
        self.code_text.configure(yscrollcommand=scrollbar.set)
        
        self.code_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 提示信息
        hint_label = ttk.Label(
            main_frame,
            text="提示：请粘贴完整的验证码，包括所有换行符",
            font=('Arial', 9),
            foreground='gray'
        )
        hint_label.pack(pady=(0, 15))
        
        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)
        
        # 确定按钮
        ok_btn = ttk.Button(
            btn_frame,
            text="验证",
            command=lambda: self.close_dialog(root, True),
            width=15
        )
        ok_btn.pack(side=tk.RIGHT, padx=(15, 0))
        
        # 取消按钮
        cancel_btn = ttk.Button(
            btn_frame,
            text="取消",
            command=lambda: self.close_dialog(root, False),
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT)
        
        # 清空按钮
        clear_btn = ttk.Button(
            btn_frame,
            text="清空",
            command=self.clear_text,
            width=15
        )
        clear_btn.pack(side=tk.LEFT)
        
        # 焦点设置到文本框
        self.code_text.focus()
        
        # 绑定快捷键
        root.bind('<Control-Return>', lambda e: self.close_dialog(root, True))
        root.bind('<Escape>', lambda e: self.close_dialog(root, False))
        
        # 等待对话框关闭
        root.wait_window()
        return self.license_code
    
    def clear_text(self):
        """清空文本框"""
        self.code_text.delete(1.0, tk.END)
        self.code_text.focus()
    
    def close_dialog(self, root, submit):
        """关闭对话框"""
        if submit:
            self.license_code = self.code_text.get(1.0, tk.END).strip()
            if not self.license_code:
                messagebox.showwarning("警告", "请输入验证码", parent=root)
                return
        else:
            self.license_code = None
        root.destroy()


# ================== 许可证管理模块 ==================
class LicenseManager:
    def __init__(self):
        self.machine_code = MachineInfo.get_machine_code()
        self.public_key = None
        self.load_public_key()
    
    def load_public_key(self):
        """加载嵌入的公钥"""
        try:
            self.public_key = CryptoUtils.load_public_key(EMBEDDED_PUBLIC_KEY)
        except Exception as e:
            messagebox.showerror("错误", f"加载公钥失败：{str(e)}")
            sys.exit(1)
    
    def check_local_license(self):
        """检查本地许可证"""
        if not os.path.exists(LICENSE_FILE):
            return False, "许可证文件不存在"
        
        try:
            with open(LICENSE_FILE, 'r', encoding='utf-8') as f:
                encrypted_license = f.read()
            
            # 解密许可证
            decrypted_data = CryptoUtils.decrypt_data(encrypted_license, self.machine_code)
            if not decrypted_data:
                # 如果解密失败，可能是machine_code变了或者文件损坏，可以尝试删除旧的无效许可证文件
                try:
                    os.remove(LICENSE_FILE)
                    print(f"无效的许可证文件 {LICENSE_FILE} 已删除。")
                except OSError as e_remove:
                    print(f"删除无效许可证文件失败: {e_remove}")
                return False, "许可证解密失败"
            
            license_info = json.loads(decrypted_data)
            
            # 验证机器码
            if license_info.get('machine_code') != self.machine_code:
                return False, "许可证与当前机器不匹配"
            
            # 检查有效期
            expire_date_str = license_info.get('expire_date')
            if expire_date_str:
                expire_date = datetime.fromisoformat(expire_date_str)
                if datetime.now() > expire_date:
                    return False, "许可证已过期"
            
            return True, "许可证有效"
            
        except Exception as e:
            return False, f"验证许可证时出错：{str(e)}"
    
    def verify_license_code(self, license_code):
        """验证许可证代码"""
        try:
            # 清理输入（移除空白字符和换行）
            clean_code = ''.join(license_code.split())
            
            # Base64解码
            license_json = base64.b64decode(clean_code).decode('utf-8')
            license_data = json.loads(license_json)
            
            # 提取数据和签名
            data = license_data.get('data')
            signature_b64 = license_data.get('signature')
            
            if not data or not signature_b64:
                return False, "许可证格式错误"
            
            # 验证机器码
            if data.get('machine_code') != self.machine_code:
                return False, "许可证与当前机器不匹配"
            
            # 验证有效期
            expire_date_str = data.get('expire_date')
            if expire_date_str:
                expire_date = datetime.fromisoformat(expire_date_str)
                if datetime.now() > expire_date:
                    return False, "许可证已过期"
            
            # 验证签名
            data_json = json.dumps(data, sort_keys=True)
            data_bytes = data_json.encode('utf-8')
            signature_bytes = base64.b64decode(signature_b64)
            
            if not CryptoUtils.verify_signature(self.public_key, data_bytes, signature_bytes):
                return False, "许可证签名验证失败"
            
            # 保存有效的许可证到本地
            self.save_license(data)
            
            return True, "许可证验证成功"
            
        except Exception as e:
            return False, f"验证许可证时出错：{str(e)}"
    
    def save_license(self, license_data):
        """保存许可证到本地"""
        try:
            license_json = json.dumps(license_data)
            encrypted_license = CryptoUtils.encrypt_data(license_json, self.machine_code)
            
            with open(LICENSE_FILE, 'w', encoding='utf-8') as f:
                f.write(encrypted_license)
            print(f"许可证已成功保存到: {LICENSE_FILE}")
        except Exception as e:
            # 确保在主线程中显示错误，如果 LicenseManager 可能在其他线程中调用
            # 但这里 save_license 是在主流程中
            messagebox.showerror("保存许可证失败", f"无法保存许可证文件到 {LICENSE_FILE}。\n错误: {str(e)}\n请检查是否有写入权限。")
            raise Exception(f"保存许可证失败：{str(e)}")
    
    def show_license_dialog(self):
        """显示许可证输入对话框"""
        # 显示机器码对话框
        machine_dialog = MachineCodeDialog(self.machine_code)
        if not machine_dialog.show():
            return False
        
        # 循环输入验证码直到成功或取消
        while True:
            license_dialog = LicenseCodeDialog()
            license_code = license_dialog.show()
            
            if license_code is None:  # 用户点击取消
                return False
            
            success, message = self.verify_license_code(license_code)
            if success:
                messagebox.showinfo("成功", "许可证验证成功！程序即将启动。")
                return True
            else:
                # 显示错误信息并询问是否重试
                retry = messagebox.askyesno(
                    "验证失败", 
                    f"验证失败：{message}\n\n是否重新输入验证码？"
                )
                if not retry:
                    return False


# ================== 文件监控处理模块 ==================
def get_default_download_path():
    """获取系统默认下载目录"""
    if sys.platform == "win32":
        # Windows
        return str(Path.home() / "Downloads")
    elif sys.platform == "darwin":
        # macOS
        return str(Path.home() / "Downloads")
    else:
        # Linux
        downloads_path = Path.home() / "Downloads"
        if downloads_path.exists():
            return str(downloads_path)
        else:
            return str(Path.home())


class DownloadHandler(FileSystemEventHandler):
    def __init__(self, monitor_path, target_path):
        self.monitor_path = monitor_path  # 监控的下载目录
        self.target_path = target_path    # 目标移动目录（脚本目录）
        self.processed_files = set()
        
    def on_created(self, event):
        if event.is_directory:
            return
            
        file_path = event.src_path
        file_name = os.path.basename(file_path)
        
        # 避免重复处理同一个文件
        if file_path in self.processed_files:
            return
            
        # 等待文件完全下载完成（避免处理临时文件）
        def process_file():
            time.sleep(2)  # 增加等待时间，确保文件完全下载
            
            # 检查文件是否仍然存在且不是临时文件
            if not os.path.exists(file_path):
                return
                
            # 跳过临时文件（通常以.tmp, .crdownload, .part结尾）
            if file_name.endswith(('.tmp', '.crdownload', '.part')):
                return
            
            # 获取文件大小，确保文件下载完成
            try:
                initial_size = os.path.getsize(file_path)
                time.sleep(0.5)
                final_size = os.path.getsize(file_path)
                if initial_size != final_size:
                    return  # 文件还在下载中
            except:
                return
                
            # 生成新的文件名（将扩展名改为.sql）
            name_without_ext = os.path.splitext(file_name)
            new_file_name = f"{name_without_ext}.sql"
            new_file_path = os.path.join(self.target_path, new_file_name)
            
            # 如果目标文件已存在，添加数字后缀
            counter = 1
            while os.path.exists(new_file_path):
                new_file_name = f"{name_without_ext}_{counter}.sql"
                new_file_path = os.path.join(self.target_path, new_file_name)
                counter += 1
            
            try:
                # 确保目标目录存在 (SCRIPT_DIR)
                if not os.path.exists(self.target_path):
                    try:
                        os.makedirs(self.target_path)
                        print(f"目标目录 {self.target_path} 已创建。")
                    except OSError as e_mkdir:
                        print(f"无法创建目标目录 {self.target_path}: {e_mkdir}")
                        messagebox.showerror("目录错误", f"无法创建目录 {self.target_path} 用于保存文件。\n请检查权限或手动创建该目录。")
                        return


                # 如果监控目录和目标目录不同，移动并重命名文件
                if self.monitor_path != self.target_path:
                    # 使用shutil.move()支持跨驱动器移动
                    shutil.move(file_path, new_file_path)
                    print(f"文件已移动并重命名: {file_name} -> {self.target_path}/{new_file_name}")
                else:
                    # 同目录重命名
                    if not file_name.lower().endswith('.sql'):
                        os.rename(file_path, new_file_path)
                        print(f"文件已重命名: {file_name} -> {new_file_name}")
                
                self.processed_files.add(file_path)
            except Exception as e:
                print(f"处理文件时出错: {e}")
                messagebox.showerror("文件处理错误", f"处理文件 {file_name} 时出错: {e}")

        
        # 在新线程中处理文件，避免阻塞监控
        threading.Thread(target=process_file, daemon=True).start()


def start_download_monitor(monitor_path, target_path):
    """启动下载文件夹监控"""
    event_handler = DownloadHandler(monitor_path, target_path)
    observer = Observer()
    observer.schedule(event_handler, monitor_path, recursive=False)
    observer.start()
    print(f"开始监控下载目录: {monitor_path}")
    print(f"文件将被处理并保存到: {target_path}") # 修改了日志信息
    return observer


# ================== WebView相关 ==================
HIDE_HELP_BUTTON_JS = """
function findHelpButton() {
    const possibleHelpButtons = document.querySelectorAll('div[aria-haspopup="true"]');
    for (let i = 0; i < possibleHelpButtons.length; i++) {
        const el = possibleHelpButtons[i];
        if (el.textContent.trim() === '帮助') {
            return el;
        }
    }
    return null;
}
function hideHelpButtonWithRetry(attemptsLeft = 20) {
    const helpButton = findHelpButton();
    if (helpButton) {
        if (helpButton.style.display !== 'none') {
            helpButton.style.display = 'none';
        }
        return;
    } else {
        if (attemptsLeft > 0) {
            setTimeout(() => hideHelpButtonWithRetry(attemptsLeft - 1), 100);
        }
    }
}

function findLogoImage() {
    return document.querySelector('img[alt="logo"]');
}

function hideLogoImageWithRetry(attemptsLeft = 20) {
    const logoImage = findLogoImage();
    if (logoImage) {
        if (logoImage.style.display !== 'none') {
            logoImage.style.display = 'none';
        }
        return;
    } else {
        if (attemptsLeft > 0) {
            setTimeout(() => hideLogoImageWithRetry(attemptsLeft - 1), 100);
        }
    }
}

function findShareButton() {
    const buttons = document.querySelectorAll('button.semi-button.semi-button-primary.semi-button-light.semi-button-with-icon');
    for (let i = 0; i < buttons.length; i++) {
        const btn = buttons[i];
        const textSpan = btn.querySelector('span.semi-button-content-right');
        if (textSpan && textSpan.textContent.trim() === '分享') {
            const shareIcon = btn.querySelector('span[role="img"][aria-label="share_stroked"]');
            if (shareIcon) {
                return btn;
            }
        }
    }
    const allButtons = document.querySelectorAll('button');
    for (let button of allButtons) {
        if (button.textContent.includes('分享') && button.querySelector('span[role="img"][aria-label="share_stroked"]')) {
            if (button.classList.contains('semi-button-primary') && button.classList.contains('semi-button-light')) {
                 return button;
            }
        }
    }
    return null;
}

function hideShareButtonWithRetry(attemptsLeft = 20) {
    const shareButton = findShareButton();
    if (shareButton) {
        if (shareButton.style.display !== 'none') {
            shareButton.style.display = 'none';
        }
        return;
    } else {
        if (attemptsLeft > 0) {
            setTimeout(() => hideShareButtonWithRetry(attemptsLeft - 1), 100);
        }
    }
}

// =================== 新增的功能：隐藏“代码”按钮 ===================
function findCodeButton() {
    // 这个按钮的唯一标识是其内部的 'code' 图标
    const codeIcon = document.querySelector('span[role="img"][aria-label="code"]');
    if (codeIcon) {
        // 返回包含该图标的最近的父 <button> 元素
        return codeIcon.closest('button');
    }
    return null;
}

function hideCodeButtonWithRetry(attemptsLeft = 20) {
    const codeButton = findCodeButton();
    if (codeButton) {
        if (codeButton.style.display !== 'none') {
            codeButton.style.display = 'none';
            console.log('Code button hidden.');
        }
        return;
    } else {
        if (attemptsLeft > 0) {
            setTimeout(() => hideCodeButtonWithRetry(attemptsLeft - 1), 100);
        }
    }
}
// =================================================================


const customInfoHTMLString = `<div id="custom-footer-content" style="text-align: center; padding: 24px 0; margin-top: 30px; border-top: 1px solid #eee;">Made by George.Guo 202508 | Version: 1.2 | <a href="http://23.94.56.51:8899/wp-content/uploads/index.html" target="_blank">产品介绍</a></div>`;

function addCustomInfoElement() {
    if (document.getElementById('custom-footer-content')) {
        return; // Already added
    }

    const tempContainer = document.createElement('div');
    tempContainer.innerHTML = customInfoHTMLString.trim();
    const infoElement = tempContainer.firstChild;

    if (!infoElement) {
        console.error('Failed to create custom info element from HTML string.');
        return;
    }

    let targetParentForHeader = null;
    const shareBtn = findShareButton(); 
    if (shareBtn && shareBtn.parentElement) {
        targetParentForHeader = shareBtn.parentElement;
    } else {
        const helpBtn = findHelpButton(); 
        if (helpBtn && helpBtn.parentElement) {
            targetParentForHeader = helpBtn.parentElement;
        } else {
            const githubLink = document.querySelector('header a[href*="github.com/drawdb-io/drawdb"]');
            if (githubLink && githubLink.parentElement) {
                targetParentForHeader = githubLink.parentElement;
            }
        }
    }
    
    if (targetParentForHeader && typeof targetParentForHeader.classList !== 'undefined' && targetParentForHeader.classList.contains('flex')) {
        infoElement.style.textAlign = 'left';
        infoElement.style.padding = '0';
        infoElement.style.marginTop = '0';
        infoElement.style.borderTop = 'none';
        infoElement.style.fontSize = '12px';
        infoElement.style.color = '#333';
        infoElement.style.marginLeft = '1rem'; 
        infoElement.style.display = 'flex'; 
        infoElement.style.alignItems = 'center';

        const link = infoElement.querySelector('a');
        if (link) {
            link.style.color = '#007bff';
            link.style.textDecoration = 'underline';
            link.style.marginLeft = '5px'; 
        }
        
        const githubLinkInParent = targetParentForHeader.querySelector('a[href*="github.com/drawdb-io/drawdb"]');
        if (githubLinkInParent) {
            targetParentForHeader.insertBefore(infoElement, githubLinkInParent);
        } else {
            targetParentForHeader.appendChild(infoElement);
        }
        console.log('Custom info element added to header controls.');

    } else {
        console.warn('Header placement failed for custom info. Falling back to fixed positioning.');
        infoElement.style.position = 'fixed';
        infoElement.style.top = '15px';
        infoElement.style.right = '15px';
        infoElement.style.zIndex = '10001';
        infoElement.style.backgroundColor = 'rgba(248, 249, 250, 0.95)';
        infoElement.style.padding = '10px 15px';
        infoElement.style.border = '1px solid #dee2e6';
        infoElement.style.borderRadius = '0.25rem';
        infoElement.style.boxShadow = '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
        infoElement.style.fontSize = '12px';
        infoElement.style.color = '#212529';
        infoElement.style.textAlign = 'right'; 
        infoElement.style.marginTop = '0';
        infoElement.style.borderTop = 'none';

        const link = infoElement.querySelector('a');
        if (link) {
            link.style.color = '#007bff';
            link.style.textDecoration = 'underline';
        }

        if (document.body) {
            document.body.appendChild(infoElement);
            console.log('Custom info element added with fixed positioning.');
        } else {
            console.error('document.body not available for fixed positioning.');
        }
    }
}

function addCustomInfoWithRetry(attemptsLeft = 20) {
    // Check if dependent functions are available and DOM is ready
    if (document.readyState === 'complete' && 
        typeof findShareButton === 'function' && 
        typeof findHelpButton === 'function') {
        addCustomInfoElement();
    } else if (attemptsLeft > 0) {
        setTimeout(() => addCustomInfoWithRetry(attemptsLeft - 1), 300);
    } else {
        console.error('Failed to add custom info after multiple attempts. DOM not ready or helper functions missing.');
    }
}

hideHelpButtonWithRetry();
hideLogoImageWithRetry();
hideShareButtonWithRetry();
addCustomInfoWithRetry();
hideCodeButtonWithRetry(); // 调用新增的函数来隐藏“代码”按钮
"""


def on_window_loaded(window):
    """窗口加载完成后的回调"""
    window.evaluate_js(HIDE_HELP_BUTTON_JS)


# ================== 主程序 ==================
def show_drawdb_app():
    """显示DrawDB应用"""
    # SCRIPT_DIR 现在指向可执行文件目录（如果打包）或脚本目录
    app_data_dir = SCRIPT_DIR 
    
    # 确保应用数据目录存在 (主要用于保存下载的SQL文件)
    # LicenseManager.save_license 会直接写入 LICENSE_FILE，其目录是 SCRIPT_DIR
    if not os.path.exists(app_data_dir):
        try:
            os.makedirs(app_data_dir)
            print(f"应用数据目录 {app_data_dir} 已创建。")
        except OSError as e:
            messagebox.showerror("目录错误", f"无法创建应用数据目录: {app_data_dir}\n错误: {e}\n请检查写入权限。")
            # 如果目录无法创建，后续的文件保存可能会失败，但程序仍可尝试启动
            # 或者选择在此处退出 sys.exit(1)
    
    # 获取系统默认下载目录
    default_download_path = get_default_download_path()
    print(f"系统默认下载目录: {default_download_path}")
    print(f"下载的文件将保存到目标目录: {app_data_dir}") # 使用 app_data_dir
    
    # 启动下载监控（监控默认下载目录，文件处理后移动到应用数据目录）
    observer = start_download_monitor(default_download_path, app_data_dir) # 使用 app_data_dir
    
    # 配置webview（尝试设置下载路径，但可能不生效）
    webview.settings['ALLOW_DOWNLOADS'] = True
    if hasattr(webview.settings, 'DOWNLOAD_PATH'):
        # 理想情况下，webview 下载也应指向 app_data_dir，但这通常不被 webview 稳定支持
        # webview.settings['DOWNLOAD_PATH'] = app_data_dir
        # print(f"尝试设置WebView下载路径为: {app_data_dir}")
        # 保持原样，依赖 watchdog 移动
        pass
    
    window = webview.create_window(
        "绘制实体关系图",
        "https://drawdb.aitech2025.dpdns.org/editor",
        width=1200,
        height=800,
        resizable=True,
    )
    
    window.events.loaded += on_window_loaded
    
    try:
        webview.start()
    finally:
        # 程序退出时停止监控
        observer.stop()
        observer.join()
        print("下载监控已停止")


def main():
    """主函数"""
    # 创建并隐藏Tkinter根窗口，以避免在首次运行需要显示对话框时出现额外的空白窗口。
    _main_tk_root = tk.Tk()
    _main_tk_root.withdraw()

    print("=== DrawDB 桌面应用启动 ===")
    print(f"应用程序根目录 (用于许可证和数据): {SCRIPT_DIR}") # 更新了日志信息
    print(f"许可证文件路径: {LICENSE_FILE}")
    
    # 初始化许可证管理器
    license_manager = LicenseManager()
    print(f"当前机器码: {license_manager.machine_code}")
    
    # 检查本地许可证
    is_valid, message = license_manager.check_local_license()
    
    if is_valid:
        print("本地许可证验证通过，启动程序...")
        show_drawdb_app()
    else:
        print(f"本地许可证验证失败: {message}")
        print("需要重新验证许可证...")
        
        # 显示许可证输入对话框
        if license_manager.show_license_dialog():
            print("新许可证验证成功，启动程序...")
            show_drawdb_app()
        else:
            print("用户取消验证或验证失败，程序退出")
            _main_tk_root.destroy() # 确保tk根窗口关闭
            sys.exit(0)
    
    _main_tk_root.destroy() # 确保tk根窗口在正常退出时也关闭


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        # 临时的Tk根，用于显示错误消息框
        _err_root_main = tk.Tk()
        _err_root_main.withdraw()
        print(f"程序运行出错: {str(e)}")
        messagebox.showerror("严重错误", f"程序运行出错：{str(e)}")
        _err_root_main.destroy()
        sys.exit(1)